/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as IndexImport } from './routes/index'
import { Route as UserIndexImport } from './routes/user/index'
import { Route as PostauthIndexImport } from './routes/postauth/index'
import { Route as MarketingStorageIndexImport } from './routes/marketing-storage/index'
import { Route as LandingIndexImport } from './routes/landing/index'
import { Route as AccessDeniedIndexImport } from './routes/access-denied/index'
import { Route as MarketingStorageStorageIndexImport } from './routes/marketing-storage/storage/index'
import { Route as MarketingStorageOrdersLogsIndexImport } from './routes/marketing-storage/orders-logs/index'
import { Route as MarketingStorageLogsIndexImport } from './routes/marketing-storage/logs/index'
import { Route as MarketingStorageIncomesIndexImport } from './routes/marketing-storage/incomes/index'
import { Route as MarketingStorageDeliveredRequestsIndexImport } from './routes/marketing-storage/delivered-requests/index'
import { Route as MarketingStorageCalfileIndexImport } from './routes/marketing-storage/calfile/index'
import { Route as MarketingStorageCalIndexImport } from './routes/marketing-storage/cal/index'
import { Route as MarketingStorageAccountingStorageIndexImport } from './routes/marketing-storage/accounting-storage/index'
import { Route as LandingLandingPageIndexImport } from './routes/landing/landing-page/index'

// Create/Update Routes

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const UserIndexRoute = UserIndexImport.update({
  id: '/user/',
  path: '/user/',
  getParentRoute: () => rootRoute,
} as any)

const PostauthIndexRoute = PostauthIndexImport.update({
  id: '/postauth/',
  path: '/postauth/',
  getParentRoute: () => rootRoute,
} as any)

const MarketingStorageIndexRoute = MarketingStorageIndexImport.update({
  id: '/marketing-storage/',
  path: '/marketing-storage/',
  getParentRoute: () => rootRoute,
} as any)

const LandingIndexRoute = LandingIndexImport.update({
  id: '/landing/',
  path: '/landing/',
  getParentRoute: () => rootRoute,
} as any)

const AccessDeniedIndexRoute = AccessDeniedIndexImport.update({
  id: '/access-denied/',
  path: '/access-denied/',
  getParentRoute: () => rootRoute,
} as any)

const MarketingStorageStorageIndexRoute =
  MarketingStorageStorageIndexImport.update({
    id: '/marketing-storage/storage/',
    path: '/marketing-storage/storage/',
    getParentRoute: () => rootRoute,
  } as any)

const MarketingStorageOrdersLogsIndexRoute =
  MarketingStorageOrdersLogsIndexImport.update({
    id: '/marketing-storage/orders-logs/',
    path: '/marketing-storage/orders-logs/',
    getParentRoute: () => rootRoute,
  } as any)

const MarketingStorageLogsIndexRoute = MarketingStorageLogsIndexImport.update({
  id: '/marketing-storage/logs/',
  path: '/marketing-storage/logs/',
  getParentRoute: () => rootRoute,
} as any)

const MarketingStorageIncomesIndexRoute =
  MarketingStorageIncomesIndexImport.update({
    id: '/marketing-storage/incomes/',
    path: '/marketing-storage/incomes/',
    getParentRoute: () => rootRoute,
  } as any)

const MarketingStorageDeliveredRequestsIndexRoute =
  MarketingStorageDeliveredRequestsIndexImport.update({
    id: '/marketing-storage/delivered-requests/',
    path: '/marketing-storage/delivered-requests/',
    getParentRoute: () => rootRoute,
  } as any)

const MarketingStorageCalfileIndexRoute =
  MarketingStorageCalfileIndexImport.update({
    id: '/marketing-storage/calfile/',
    path: '/marketing-storage/calfile/',
    getParentRoute: () => rootRoute,
  } as any)

const MarketingStorageCalIndexRoute = MarketingStorageCalIndexImport.update({
  id: '/marketing-storage/cal/',
  path: '/marketing-storage/cal/',
  getParentRoute: () => rootRoute,
} as any)

const MarketingStorageAccountingStorageIndexRoute =
  MarketingStorageAccountingStorageIndexImport.update({
    id: '/marketing-storage/accounting-storage/',
    path: '/marketing-storage/accounting-storage/',
    getParentRoute: () => rootRoute,
  } as any)

const LandingLandingPageIndexRoute = LandingLandingPageIndexImport.update({
  id: '/landing/landing-page/',
  path: '/landing/landing-page/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/access-denied/': {
      id: '/access-denied/'
      path: '/access-denied'
      fullPath: '/access-denied'
      preLoaderRoute: typeof AccessDeniedIndexImport
      parentRoute: typeof rootRoute
    }
    '/landing/': {
      id: '/landing/'
      path: '/landing'
      fullPath: '/landing'
      preLoaderRoute: typeof LandingIndexImport
      parentRoute: typeof rootRoute
    }
    '/marketing-storage/': {
      id: '/marketing-storage/'
      path: '/marketing-storage'
      fullPath: '/marketing-storage'
      preLoaderRoute: typeof MarketingStorageIndexImport
      parentRoute: typeof rootRoute
    }
    '/postauth/': {
      id: '/postauth/'
      path: '/postauth'
      fullPath: '/postauth'
      preLoaderRoute: typeof PostauthIndexImport
      parentRoute: typeof rootRoute
    }
    '/user/': {
      id: '/user/'
      path: '/user'
      fullPath: '/user'
      preLoaderRoute: typeof UserIndexImport
      parentRoute: typeof rootRoute
    }
    '/landing/landing-page/': {
      id: '/landing/landing-page/'
      path: '/landing/landing-page'
      fullPath: '/landing/landing-page'
      preLoaderRoute: typeof LandingLandingPageIndexImport
      parentRoute: typeof rootRoute
    }
    '/marketing-storage/accounting-storage/': {
      id: '/marketing-storage/accounting-storage/'
      path: '/marketing-storage/accounting-storage'
      fullPath: '/marketing-storage/accounting-storage'
      preLoaderRoute: typeof MarketingStorageAccountingStorageIndexImport
      parentRoute: typeof rootRoute
    }
    '/marketing-storage/cal/': {
      id: '/marketing-storage/cal/'
      path: '/marketing-storage/cal'
      fullPath: '/marketing-storage/cal'
      preLoaderRoute: typeof MarketingStorageCalIndexImport
      parentRoute: typeof rootRoute
    }
    '/marketing-storage/calfile/': {
      id: '/marketing-storage/calfile/'
      path: '/marketing-storage/calfile'
      fullPath: '/marketing-storage/calfile'
      preLoaderRoute: typeof MarketingStorageCalfileIndexImport
      parentRoute: typeof rootRoute
    }
    '/marketing-storage/delivered-requests/': {
      id: '/marketing-storage/delivered-requests/'
      path: '/marketing-storage/delivered-requests'
      fullPath: '/marketing-storage/delivered-requests'
      preLoaderRoute: typeof MarketingStorageDeliveredRequestsIndexImport
      parentRoute: typeof rootRoute
    }
    '/marketing-storage/incomes/': {
      id: '/marketing-storage/incomes/'
      path: '/marketing-storage/incomes'
      fullPath: '/marketing-storage/incomes'
      preLoaderRoute: typeof MarketingStorageIncomesIndexImport
      parentRoute: typeof rootRoute
    }
    '/marketing-storage/logs/': {
      id: '/marketing-storage/logs/'
      path: '/marketing-storage/logs'
      fullPath: '/marketing-storage/logs'
      preLoaderRoute: typeof MarketingStorageLogsIndexImport
      parentRoute: typeof rootRoute
    }
    '/marketing-storage/orders-logs/': {
      id: '/marketing-storage/orders-logs/'
      path: '/marketing-storage/orders-logs'
      fullPath: '/marketing-storage/orders-logs'
      preLoaderRoute: typeof MarketingStorageOrdersLogsIndexImport
      parentRoute: typeof rootRoute
    }
    '/marketing-storage/storage/': {
      id: '/marketing-storage/storage/'
      path: '/marketing-storage/storage'
      fullPath: '/marketing-storage/storage'
      preLoaderRoute: typeof MarketingStorageStorageIndexImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/access-denied': typeof AccessDeniedIndexRoute
  '/landing': typeof LandingIndexRoute
  '/marketing-storage': typeof MarketingStorageIndexRoute
  '/postauth': typeof PostauthIndexRoute
  '/user': typeof UserIndexRoute
  '/landing/landing-page': typeof LandingLandingPageIndexRoute
  '/marketing-storage/accounting-storage': typeof MarketingStorageAccountingStorageIndexRoute
  '/marketing-storage/cal': typeof MarketingStorageCalIndexRoute
  '/marketing-storage/calfile': typeof MarketingStorageCalfileIndexRoute
  '/marketing-storage/delivered-requests': typeof MarketingStorageDeliveredRequestsIndexRoute
  '/marketing-storage/incomes': typeof MarketingStorageIncomesIndexRoute
  '/marketing-storage/logs': typeof MarketingStorageLogsIndexRoute
  '/marketing-storage/orders-logs': typeof MarketingStorageOrdersLogsIndexRoute
  '/marketing-storage/storage': typeof MarketingStorageStorageIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/access-denied': typeof AccessDeniedIndexRoute
  '/landing': typeof LandingIndexRoute
  '/marketing-storage': typeof MarketingStorageIndexRoute
  '/postauth': typeof PostauthIndexRoute
  '/user': typeof UserIndexRoute
  '/landing/landing-page': typeof LandingLandingPageIndexRoute
  '/marketing-storage/accounting-storage': typeof MarketingStorageAccountingStorageIndexRoute
  '/marketing-storage/cal': typeof MarketingStorageCalIndexRoute
  '/marketing-storage/calfile': typeof MarketingStorageCalfileIndexRoute
  '/marketing-storage/delivered-requests': typeof MarketingStorageDeliveredRequestsIndexRoute
  '/marketing-storage/incomes': typeof MarketingStorageIncomesIndexRoute
  '/marketing-storage/logs': typeof MarketingStorageLogsIndexRoute
  '/marketing-storage/orders-logs': typeof MarketingStorageOrdersLogsIndexRoute
  '/marketing-storage/storage': typeof MarketingStorageStorageIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/access-denied/': typeof AccessDeniedIndexRoute
  '/landing/': typeof LandingIndexRoute
  '/marketing-storage/': typeof MarketingStorageIndexRoute
  '/postauth/': typeof PostauthIndexRoute
  '/user/': typeof UserIndexRoute
  '/landing/landing-page/': typeof LandingLandingPageIndexRoute
  '/marketing-storage/accounting-storage/': typeof MarketingStorageAccountingStorageIndexRoute
  '/marketing-storage/cal/': typeof MarketingStorageCalIndexRoute
  '/marketing-storage/calfile/': typeof MarketingStorageCalfileIndexRoute
  '/marketing-storage/delivered-requests/': typeof MarketingStorageDeliveredRequestsIndexRoute
  '/marketing-storage/incomes/': typeof MarketingStorageIncomesIndexRoute
  '/marketing-storage/logs/': typeof MarketingStorageLogsIndexRoute
  '/marketing-storage/orders-logs/': typeof MarketingStorageOrdersLogsIndexRoute
  '/marketing-storage/storage/': typeof MarketingStorageStorageIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/access-denied'
    | '/landing'
    | '/marketing-storage'
    | '/postauth'
    | '/user'
    | '/landing/landing-page'
    | '/marketing-storage/accounting-storage'
    | '/marketing-storage/cal'
    | '/marketing-storage/calfile'
    | '/marketing-storage/delivered-requests'
    | '/marketing-storage/incomes'
    | '/marketing-storage/logs'
    | '/marketing-storage/orders-logs'
    | '/marketing-storage/storage'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/access-denied'
    | '/landing'
    | '/marketing-storage'
    | '/postauth'
    | '/user'
    | '/landing/landing-page'
    | '/marketing-storage/accounting-storage'
    | '/marketing-storage/cal'
    | '/marketing-storage/calfile'
    | '/marketing-storage/delivered-requests'
    | '/marketing-storage/incomes'
    | '/marketing-storage/logs'
    | '/marketing-storage/orders-logs'
    | '/marketing-storage/storage'
  id:
    | '__root__'
    | '/'
    | '/access-denied/'
    | '/landing/'
    | '/marketing-storage/'
    | '/postauth/'
    | '/user/'
    | '/landing/landing-page/'
    | '/marketing-storage/accounting-storage/'
    | '/marketing-storage/cal/'
    | '/marketing-storage/calfile/'
    | '/marketing-storage/delivered-requests/'
    | '/marketing-storage/incomes/'
    | '/marketing-storage/logs/'
    | '/marketing-storage/orders-logs/'
    | '/marketing-storage/storage/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AccessDeniedIndexRoute: typeof AccessDeniedIndexRoute
  LandingIndexRoute: typeof LandingIndexRoute
  MarketingStorageIndexRoute: typeof MarketingStorageIndexRoute
  PostauthIndexRoute: typeof PostauthIndexRoute
  UserIndexRoute: typeof UserIndexRoute
  LandingLandingPageIndexRoute: typeof LandingLandingPageIndexRoute
  MarketingStorageAccountingStorageIndexRoute: typeof MarketingStorageAccountingStorageIndexRoute
  MarketingStorageCalIndexRoute: typeof MarketingStorageCalIndexRoute
  MarketingStorageCalfileIndexRoute: typeof MarketingStorageCalfileIndexRoute
  MarketingStorageDeliveredRequestsIndexRoute: typeof MarketingStorageDeliveredRequestsIndexRoute
  MarketingStorageIncomesIndexRoute: typeof MarketingStorageIncomesIndexRoute
  MarketingStorageLogsIndexRoute: typeof MarketingStorageLogsIndexRoute
  MarketingStorageOrdersLogsIndexRoute: typeof MarketingStorageOrdersLogsIndexRoute
  MarketingStorageStorageIndexRoute: typeof MarketingStorageStorageIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AccessDeniedIndexRoute: AccessDeniedIndexRoute,
  LandingIndexRoute: LandingIndexRoute,
  MarketingStorageIndexRoute: MarketingStorageIndexRoute,
  PostauthIndexRoute: PostauthIndexRoute,
  UserIndexRoute: UserIndexRoute,
  LandingLandingPageIndexRoute: LandingLandingPageIndexRoute,
  MarketingStorageAccountingStorageIndexRoute:
    MarketingStorageAccountingStorageIndexRoute,
  MarketingStorageCalIndexRoute: MarketingStorageCalIndexRoute,
  MarketingStorageCalfileIndexRoute: MarketingStorageCalfileIndexRoute,
  MarketingStorageDeliveredRequestsIndexRoute:
    MarketingStorageDeliveredRequestsIndexRoute,
  MarketingStorageIncomesIndexRoute: MarketingStorageIncomesIndexRoute,
  MarketingStorageLogsIndexRoute: MarketingStorageLogsIndexRoute,
  MarketingStorageOrdersLogsIndexRoute: MarketingStorageOrdersLogsIndexRoute,
  MarketingStorageStorageIndexRoute: MarketingStorageStorageIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/access-denied/",
        "/landing/",
        "/marketing-storage/",
        "/postauth/",
        "/user/",
        "/landing/landing-page/",
        "/marketing-storage/accounting-storage/",
        "/marketing-storage/cal/",
        "/marketing-storage/calfile/",
        "/marketing-storage/delivered-requests/",
        "/marketing-storage/incomes/",
        "/marketing-storage/logs/",
        "/marketing-storage/orders-logs/",
        "/marketing-storage/storage/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/access-denied/": {
      "filePath": "access-denied/index.tsx"
    },
    "/landing/": {
      "filePath": "landing/index.tsx"
    },
    "/marketing-storage/": {
      "filePath": "marketing-storage/index.tsx"
    },
    "/postauth/": {
      "filePath": "postauth/index.tsx"
    },
    "/user/": {
      "filePath": "user/index.tsx"
    },
    "/landing/landing-page/": {
      "filePath": "landing/landing-page/index.tsx"
    },
    "/marketing-storage/accounting-storage/": {
      "filePath": "marketing-storage/accounting-storage/index.tsx"
    },
    "/marketing-storage/cal/": {
      "filePath": "marketing-storage/cal/index.tsx"
    },
    "/marketing-storage/calfile/": {
      "filePath": "marketing-storage/calfile/index.tsx"
    },
    "/marketing-storage/delivered-requests/": {
      "filePath": "marketing-storage/delivered-requests/index.tsx"
    },
    "/marketing-storage/incomes/": {
      "filePath": "marketing-storage/incomes/index.tsx"
    },
    "/marketing-storage/logs/": {
      "filePath": "marketing-storage/logs/index.tsx"
    },
    "/marketing-storage/orders-logs/": {
      "filePath": "marketing-storage/orders-logs/index.tsx"
    },
    "/marketing-storage/storage/": {
      "filePath": "marketing-storage/storage/index.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
