{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "routes": "npx @tanstack/router-cli generate"}, "dependencies": {"@mantine/carousel": "^7.17.2", "@mantine/core": "^7.17.2", "@mantine/dates": "^7.17.2", "@mantine/dropzone": "^7.17.2", "@mantine/form": "^7.17.2", "@mantine/hooks": "^7.17.2", "@mantine/modals": "^7.17.2", "@mantine/notifications": "^7.17.2", "@tabler/icons-react": "^3.31.0", "@tailwindcss/vite": "^4.0.14", "@tanstack/react-query": "^5.68.0", "axios": "^1.8.3", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^7.1.0", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-easy-crop": "^5.4.2", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "socket.io-client": "^4.8.1", "tailwindcss": "^4.0.14", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/js": "^9.17.0", "@tanstack/react-router": "^1.114.23", "@tanstack/router-plugin": "^1.114.23", "@types/express": "^5.0.1", "@types/lodash": "^4.17.20", "@types/multer": "^1.4.12", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.14.0", "husky": "^9.1.7", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.6.2", "typescript-eslint": "^8.26.1", "vite": "^6.0.5"}}